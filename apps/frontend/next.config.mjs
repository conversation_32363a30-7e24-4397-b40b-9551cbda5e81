/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true, // Keep disabled for now to avoid blocking builds
  },
  typescript: {
    ignoreBuildErrors: true, // Keep disabled for now to avoid blocking builds
  },
  images: {
    unoptimized: true,
  },
  // API rewrites removed - using direct backend connections for local development
  // Production optimizations
  // experimental: {
  //   optimizeCss: true, // Disabled due to critters dependency issue
  // },
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  // Performance optimizations
  poweredByHeader: false,
  compress: true,
}

export default nextConfig

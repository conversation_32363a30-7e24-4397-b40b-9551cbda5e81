from django.db import models
from django.conf import settings
from django.core.validators import MinValueValidator, MaxValueValidator
from simple_history.models import HistoricalRecords
from djmoney.models.fields import MoneyField
from decimal import Decimal


class RevenueStream(models.Model):
    """Revenue tracking model for different income sources"""

    class Type(models.TextChoices):
        PROJECT_PAYMENT = 'project_payment', 'دفعة مشروع'
        SUBSCRIPTION = 'subscription', 'اشتراك'
        MAINTENANCE = 'maintenance', 'صيانة'
        CONSULTATION = 'consultation', 'استشارة'
        HOSTING = 'hosting', 'استضافة'
        DOMAIN = 'domain', 'نطاق'
        OTHER = 'other', 'أخرى'

    class Status(models.TextChoices):
        PENDING = 'pending', 'معلق'
        RECEIVED = 'received', 'مستلم'
        OVERDUE = 'overdue', 'متأخر'
        CANCELLED = 'cancelled', 'ملغي'

    # Basic Information
    title = models.CharField(max_length=200, verbose_name='العنوان')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    type = models.CharField(
        max_length=20,
        choices=Type.choices,
        verbose_name='نوع الإيراد'
    )
    status = models.CharField(
        max_length=10,
        choices=Status.choices,
        default=Status.PENDING,
        verbose_name='الحالة'
    )

    # Financial Details
    amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        verbose_name='المبلغ'
    )
    tax_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        default=0,
        verbose_name='مبلغ الضريبة'
    )
    net_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        verbose_name='المبلغ الصافي'
    )

    # Relationships
    client = models.ForeignKey(
        'clients.Client',
        on_delete=models.CASCADE,
        related_name='revenue_streams',
        blank=True,
        null=True,
        verbose_name='العميل'
    )
    project = models.ForeignKey(
        'projects.Project',
        on_delete=models.CASCADE,
        related_name='revenue_streams',
        blank=True,
        null=True,
        verbose_name='المشروع'
    )
    sales_rep = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='revenue_streams',
        verbose_name='مندوب المبيعات'
    )

    # Timeline
    invoice_date = models.DateField(verbose_name='تاريخ الفاتورة')
    due_date = models.DateField(verbose_name='تاريخ الاستحقاق')
    payment_date = models.DateField(blank=True, null=True, verbose_name='تاريخ الدفع')

    # Tracking
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    # History tracking
    history = HistoricalRecords()

    class Meta:
        verbose_name = 'مصدر إيراد'
        verbose_name_plural = 'مصادر الإيرادات'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'due_date'], name='revenue_status_due_idx'),
            models.Index(fields=['client', 'status'], name='revenue_client_status_idx'),
            models.Index(fields=['type', 'invoice_date'], name='revenue_type_date_idx'),
        ]

    def __str__(self):
        return f"{self.title} - {self.amount}"

    def save(self, *args, **kwargs):
        # Calculate net amount
        self.net_amount = self.amount - self.tax_amount
        super().save(*args, **kwargs)

    @property
    def is_overdue(self):
        """Check if payment is overdue"""
        if self.status == self.Status.PENDING:
            from django.utils import timezone
            return timezone.now().date() > self.due_date
        return False


class ExpenseCategory(models.Model):
    """Expense categories for better organization"""

    name = models.CharField(max_length=100, verbose_name='اسم الفئة')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='subcategories',
        verbose_name='الفئة الأب'
    )
    is_active = models.BooleanField(default=True, verbose_name='نشط')

    # Tracking
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'فئة مصروف'
        verbose_name_plural = 'فئات المصروفات'
        ordering = ['name']

    def __str__(self):
        if self.parent:
            return f"{self.parent.name} - {self.name}"
        return self.name


class Expense(models.Model):
    """Expense tracking model for all business expenses"""

    class Type(models.TextChoices):
        SALARY = 'salary', 'راتب'
        OFFICE_RENT = 'office_rent', 'إيجار مكتب'
        UTILITIES = 'utilities', 'مرافق'
        SOFTWARE = 'software', 'برمجيات'
        MARKETING = 'marketing', 'تسويق'
        TRAVEL = 'travel', 'سفر'
        EQUIPMENT = 'equipment', 'معدات'
        TRAINING = 'training', 'تدريب'
        LEGAL = 'legal', 'قانوني'
        ACCOUNTING = 'accounting', 'محاسبة'
        OTHER = 'other', 'أخرى'

    class Status(models.TextChoices):
        PENDING = 'pending', 'معلق'
        APPROVED = 'approved', 'موافق عليه'
        PAID = 'paid', 'مدفوع'
        REJECTED = 'rejected', 'مرفوض'

    # Basic Information
    title = models.CharField(max_length=200, verbose_name='العنوان')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    type = models.CharField(
        max_length=20,
        choices=Type.choices,
        verbose_name='نوع المصروف'
    )
    category = models.ForeignKey(
        ExpenseCategory,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='expenses',
        verbose_name='الفئة'
    )
    status = models.CharField(
        max_length=10,
        choices=Status.choices,
        default=Status.PENDING,
        verbose_name='الحالة'
    )

    # Financial Details
    amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        verbose_name='المبلغ'
    )
    tax_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        default=0,
        verbose_name='مبلغ الضريبة'
    )
    net_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        verbose_name='المبلغ الصافي'
    )

    # Relationships
    team_member = models.ForeignKey(
        'team.TeamMember',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='expenses',
        verbose_name='عضو الفريق'
    )
    project = models.ForeignKey(
        'projects.Project',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='expenses',
        verbose_name='المشروع'
    )
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_expenses',
        verbose_name='موافق عليه من'
    )

    # Timeline
    expense_date = models.DateField(verbose_name='تاريخ المصروف')
    due_date = models.DateField(blank=True, null=True, verbose_name='تاريخ الاستحقاق')
    payment_date = models.DateField(blank=True, null=True, verbose_name='تاريخ الدفع')

    # Tracking
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    # History tracking
    history = HistoricalRecords()

    class Meta:
        verbose_name = 'مصروف'
        verbose_name_plural = 'المصروفات'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'expense_date'], name='expense_status_date_idx'),
            models.Index(fields=['type', 'status'], name='expense_type_status_idx'),
            models.Index(fields=['team_member', 'expense_date'], name='expense_member_date_idx'),
        ]

    def __str__(self):
        return f"{self.title} - {self.amount}"

    def save(self, *args, **kwargs):
        # Calculate net amount
        self.net_amount = self.amount + self.tax_amount
        super().save(*args, **kwargs)


class CashFlowProjection(models.Model):
    """Cash flow projections for financial planning"""

    class Period(models.TextChoices):
        MONTHLY = 'monthly', 'شهري'
        QUARTERLY = 'quarterly', 'ربع سنوي'
        YEARLY = 'yearly', 'سنوي'

    # Basic Information
    title = models.CharField(max_length=200, verbose_name='العنوان')
    period_type = models.CharField(
        max_length=10,
        choices=Period.choices,
        verbose_name='نوع الفترة'
    )
    period_start = models.DateField(verbose_name='بداية الفترة')
    period_end = models.DateField(verbose_name='نهاية الفترة')

    # Financial Projections
    projected_revenue = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        verbose_name='الإيرادات المتوقعة'
    )
    projected_expenses = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        verbose_name='المصروفات المتوقعة'
    )
    projected_profit = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        verbose_name='الربح المتوقع'
    )

    # Actual Results (filled as period progresses)
    actual_revenue = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        default=0,
        verbose_name='الإيرادات الفعلية'
    )
    actual_expenses = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        default=0,
        verbose_name='المصروفات الفعلية'
    )
    actual_profit = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        default=0,
        verbose_name='الربح الفعلي'
    )

    # Tracking
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'توقع التدفق النقدي'
        verbose_name_plural = 'توقعات التدفق النقدي'
        ordering = ['-period_start']
        unique_together = ['period_type', 'period_start']

    def __str__(self):
        return f"{self.title} ({self.period_start} - {self.period_end})"

    def save(self, *args, **kwargs):
        # Calculate projected and actual profit
        self.projected_profit = self.projected_revenue - self.projected_expenses
        self.actual_profit = self.actual_revenue - self.actual_expenses
        super().save(*args, **kwargs)

    @property
    def revenue_variance(self):
        """Calculate revenue variance (actual vs projected)"""
        if self.projected_revenue:
            return ((self.actual_revenue - self.projected_revenue) / self.projected_revenue) * 100
        return 0

    @property
    def expense_variance(self):
        """Calculate expense variance (actual vs projected)"""
        if self.projected_expenses:
            return ((self.actual_expenses - self.projected_expenses) / self.projected_expenses) * 100
        return 0


class Budget(models.Model):
    """Budget planning and tracking model"""

    class Status(models.TextChoices):
        DRAFT = 'draft', 'مسودة'
        ACTIVE = 'active', 'نشط'
        COMPLETED = 'completed', 'مكتمل'
        CANCELLED = 'cancelled', 'ملغي'

    # Basic Information
    name = models.CharField(max_length=200, verbose_name='اسم الميزانية')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    status = models.CharField(
        max_length=10,
        choices=Status.choices,
        default=Status.DRAFT,
        verbose_name='الحالة'
    )

    # Timeline
    start_date = models.DateField(verbose_name='تاريخ البداية')
    end_date = models.DateField(verbose_name='تاريخ النهاية')

    # Budget Amounts
    total_budget = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        verbose_name='إجمالي الميزانية'
    )
    allocated_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        default=0,
        verbose_name='المبلغ المخصص'
    )
    spent_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        default=0,
        verbose_name='المبلغ المنفق'
    )
    remaining_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        default=0,
        verbose_name='المبلغ المتبقي'
    )

    # Relationships
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_budgets',
        verbose_name='منشئ الميزانية'
    )

    # Tracking
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    # History tracking
    history = HistoricalRecords()

    class Meta:
        verbose_name = 'ميزانية'
        verbose_name_plural = 'الميزانيات'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.start_date} - {self.end_date})"

    def save(self, *args, **kwargs):
        # Calculate remaining amount
        self.remaining_amount = self.total_budget - self.spent_amount
        super().save(*args, **kwargs)

    @property
    def utilization_percentage(self):
        """Calculate budget utilization percentage"""
        if self.total_budget:
            return (self.spent_amount / self.total_budget) * 100
        return 0

    @property
    def is_over_budget(self):
        """Check if budget is exceeded"""
        return self.spent_amount > self.total_budget


class BudgetAllocation(models.Model):
    """Budget allocation to different categories"""

    budget = models.ForeignKey(
        Budget,
        on_delete=models.CASCADE,
        related_name='allocations',
        verbose_name='الميزانية'
    )
    category = models.ForeignKey(
        ExpenseCategory,
        on_delete=models.CASCADE,
        related_name='budget_allocations',
        verbose_name='الفئة'
    )
    allocated_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        verbose_name='المبلغ المخصص'
    )
    spent_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        default=0,
        verbose_name='المبلغ المنفق'
    )

    # Tracking
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'تخصيص ميزانية'
        verbose_name_plural = 'تخصيصات الميزانية'
        unique_together = ['budget', 'category']

    def __str__(self):
        return f"{self.budget.name} - {self.category.name}"

    @property
    def remaining_amount(self):
        """Calculate remaining amount in allocation"""
        return self.allocated_amount - self.spent_amount

    @property
    def utilization_percentage(self):
        """Calculate allocation utilization percentage"""
        if self.allocated_amount:
            return (self.spent_amount / self.allocated_amount) * 100
        return 0


class FinancialKPI(models.Model):
    """Financial Key Performance Indicators tracking"""

    class KPIType(models.TextChoices):
        REVENUE_GROWTH = 'revenue_growth', 'نمو الإيرادات'
        PROFIT_MARGIN = 'profit_margin', 'هامش الربح'
        CASH_FLOW = 'cash_flow', 'التدفق النقدي'
        CLIENT_LTV = 'client_ltv', 'القيمة الدائمة للعميل'
        BURN_RATE = 'burn_rate', 'معدل الحرق'
        ROI = 'roi', 'العائد على الاستثمار'
        COST_PER_ACQUISITION = 'cost_per_acquisition', 'تكلفة الاكتساب'

    # Basic Information
    name = models.CharField(max_length=100, verbose_name='اسم المؤشر')
    type = models.CharField(
        max_length=25,
        choices=KPIType.choices,
        verbose_name='نوع المؤشر'
    )
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')

    # KPI Values
    target_value = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='القيمة المستهدفة'
    )
    current_value = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name='القيمة الحالية'
    )
    previous_value = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name='القيمة السابقة'
    )

    # Timeline
    period_start = models.DateField(verbose_name='بداية الفترة')
    period_end = models.DateField(verbose_name='نهاية الفترة')

    # Tracking
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'مؤشر أداء مالي'
        verbose_name_plural = 'مؤشرات الأداء المالي'
        ordering = ['-period_start']
        unique_together = ['type', 'period_start']

    def __str__(self):
        return f"{self.name} ({self.period_start})"

    @property
    def achievement_percentage(self):
        """Calculate achievement percentage against target"""
        if self.target_value:
            return (self.current_value / self.target_value) * 100
        return 0

    @property
    def growth_rate(self):
        """Calculate growth rate from previous period"""
        if self.previous_value:
            return ((self.current_value - self.previous_value) / self.previous_value) * 100
        return 0

    @property
    def status(self):
        """Get KPI status based on achievement"""
        achievement = self.achievement_percentage
        if achievement >= 100:
            return 'excellent'
        elif achievement >= 80:
            return 'good'
        elif achievement >= 60:
            return 'average'
        else:
            return 'poor'

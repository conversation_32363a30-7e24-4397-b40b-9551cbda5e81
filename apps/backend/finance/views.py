from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Count, Avg, Sum, Q
from django.utils import timezone
from datetime import datetime, timedelta, date
from decimal import Decimal

from .models import (
    RevenueStream, Expense, ExpenseCategory, CashFlowProjection,
    Budget, BudgetAllocation, FinancialKPI
)
from .serializers import (
    RevenueStreamSerializer, RevenueStreamListSerializer,
    ExpenseSerializer, ExpenseListSerializer, ExpenseCategorySerializer,
    CashFlowProjectionSerializer, BudgetSerializer, BudgetListSerializer,
    BudgetAllocationSerializer, FinancialKPISerializer,
    FinancialSummarySerializer, DepartmentFinancialSerializer
)


class RevenueStreamViewSet(viewsets.ModelViewSet):
    """Revenue stream management viewset"""
    queryset = RevenueStream.objects.all().select_related('client', 'project', 'sales_rep').order_by('-created_at')
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['type', 'status', 'client', 'project', 'sales_rep']
    search_fields = ['title', 'description', 'client__name', 'project__name']
    ordering_fields = ['amount', 'invoice_date', 'due_date', 'created_at']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return RevenueStreamListSerializer
        return RevenueStreamSerializer

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get revenue summary statistics"""
        queryset = self.get_queryset()
        
        # Basic statistics
        total_revenue = queryset.filter(status='received').aggregate(
            total=Sum('net_amount')
        )['total'] or 0
        
        pending_revenue = queryset.filter(status='pending').aggregate(
            total=Sum('net_amount')
        )['total'] or 0
        
        overdue_count = queryset.filter(
            status='pending',
            due_date__lt=timezone.now().date()
        ).count()
        
        # Monthly revenue trend (last 6 months)
        six_months_ago = timezone.now().date() - timedelta(days=180)
        monthly_revenue = []
        
        for i in range(6):
            month_start = six_months_ago + timedelta(days=30*i)
            month_end = month_start + timedelta(days=30)
            
            month_total = queryset.filter(
                status='received',
                payment_date__gte=month_start,
                payment_date__lt=month_end
            ).aggregate(total=Sum('net_amount'))['total'] or 0
            
            monthly_revenue.append({
                'month': month_start.strftime('%Y-%m'),
                'revenue': float(month_total)
            })
        
        # Revenue by type
        revenue_by_type = queryset.filter(status='received').values('type').annotate(
            total=Sum('net_amount'),
            count=Count('id')
        ).order_by('-total')
        
        summary = {
            'total_revenue': float(total_revenue),
            'pending_revenue': float(pending_revenue),
            'overdue_count': overdue_count,
            'monthly_trend': monthly_revenue,
            'revenue_by_type': list(revenue_by_type)
        }
        
        return Response(summary)

    @action(detail=False, methods=['get'])
    def overdue(self, request):
        """Get overdue revenue streams"""
        overdue_revenues = self.get_queryset().filter(
            status='pending',
            due_date__lt=timezone.now().date()
        )
        
        serializer = self.get_serializer(overdue_revenues, many=True)
        return Response(serializer.data)


class ExpenseViewSet(viewsets.ModelViewSet):
    """Expense management viewset"""
    queryset = Expense.objects.all().select_related('category', 'team_member', 'project', 'approved_by').order_by('-created_at')
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['type', 'status', 'category', 'team_member', 'project']
    search_fields = ['title', 'description', 'team_member__user__first_name', 'team_member__user__last_name']
    ordering_fields = ['amount', 'expense_date', 'due_date', 'created_at']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return ExpenseListSerializer
        return ExpenseSerializer

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get expense summary statistics"""
        queryset = self.get_queryset()
        
        # Basic statistics
        total_expenses = queryset.filter(status='paid').aggregate(
            total=Sum('net_amount')
        )['total'] or 0
        
        pending_expenses = queryset.filter(status='pending').aggregate(
            total=Sum('net_amount')
        )['total'] or 0
        
        pending_approval = queryset.filter(status='pending').count()
        
        # Monthly expense trend (last 6 months)
        six_months_ago = timezone.now().date() - timedelta(days=180)
        monthly_expenses = []
        
        for i in range(6):
            month_start = six_months_ago + timedelta(days=30*i)
            month_end = month_start + timedelta(days=30)
            
            month_total = queryset.filter(
                status='paid',
                payment_date__gte=month_start,
                payment_date__lt=month_end
            ).aggregate(total=Sum('net_amount'))['total'] or 0
            
            monthly_expenses.append({
                'month': month_start.strftime('%Y-%m'),
                'expenses': float(month_total)
            })
        
        # Expenses by type
        expenses_by_type = queryset.filter(status='paid').values('type').annotate(
            total=Sum('net_amount'),
            count=Count('id')
        ).order_by('-total')
        
        # Expenses by department
        expenses_by_dept = queryset.filter(
            status='paid',
            team_member__isnull=False
        ).values('team_member__department').annotate(
            total=Sum('net_amount'),
            count=Count('id')
        ).order_by('-total')
        
        summary = {
            'total_expenses': float(total_expenses),
            'pending_expenses': float(pending_expenses),
            'pending_approval': pending_approval,
            'monthly_trend': monthly_expenses,
            'expenses_by_type': list(expenses_by_type),
            'expenses_by_department': list(expenses_by_dept)
        }
        
        return Response(summary)

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """Approve an expense"""
        expense = self.get_object()
        
        if expense.status != 'pending':
            return Response(
                {'error': 'يمكن الموافقة على المصروفات المعلقة فقط'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        expense.status = 'approved'
        expense.approved_by = request.user
        expense.save()
        
        serializer = self.get_serializer(expense)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """Reject an expense"""
        expense = self.get_object()
        
        if expense.status != 'pending':
            return Response(
                {'error': 'يمكن رفض المصروفات المعلقة فقط'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        expense.status = 'rejected'
        expense.approved_by = request.user
        expense.save()
        
        serializer = self.get_serializer(expense)
        return Response(serializer.data)


class ExpenseCategoryViewSet(viewsets.ModelViewSet):
    """Expense category management viewset"""
    queryset = ExpenseCategory.objects.all().order_by('name')
    serializer_class = ExpenseCategorySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [filters.SearchFilter]
    search_fields = ['name', 'description']

    @action(detail=False, methods=['get'])
    def tree(self, request):
        """Get expense categories in tree structure"""
        categories = self.get_queryset().filter(parent__isnull=True)
        
        def build_tree(category):
            data = ExpenseCategorySerializer(category).data
            children = ExpenseCategory.objects.filter(parent=category)
            if children.exists():
                data['children'] = [build_tree(child) for child in children]
            return data
        
        tree_data = [build_tree(cat) for cat in categories]
        return Response(tree_data)


class CashFlowProjectionViewSet(viewsets.ModelViewSet):
    """Cash flow projection management viewset"""
    queryset = CashFlowProjection.objects.all().order_by('-period_start')
    serializer_class = CashFlowProjectionSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['period_type']
    ordering_fields = ['period_start', 'projected_revenue', 'actual_revenue']
    ordering = ['-period_start']

    @action(detail=False, methods=['get'])
    def current_year(self, request):
        """Get cash flow projections for current year"""
        current_year = timezone.now().year
        projections = self.get_queryset().filter(
            period_start__year=current_year
        )
        
        serializer = self.get_serializer(projections, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def auto_update(self, request):
        """Auto-update actual values from revenue and expense data"""
        updated_count = 0
        
        for projection in self.get_queryset():
            # Calculate actual revenue
            actual_revenue = RevenueStream.objects.filter(
                status='received',
                payment_date__gte=projection.period_start,
                payment_date__lte=projection.period_end
            ).aggregate(total=Sum('net_amount'))['total'] or 0
            
            # Calculate actual expenses
            actual_expenses = Expense.objects.filter(
                status='paid',
                payment_date__gte=projection.period_start,
                payment_date__lte=projection.period_end
            ).aggregate(total=Sum('net_amount'))['total'] or 0
            
            # Update if values changed
            if (projection.actual_revenue != actual_revenue or 
                projection.actual_expenses != actual_expenses):
                projection.actual_revenue = actual_revenue
                projection.actual_expenses = actual_expenses
                projection.save()
                updated_count += 1
        
        return Response({
            'message': f'تم تحديث {updated_count} توقع للتدفق النقدي',
            'updated_count': updated_count
        })


class BudgetViewSet(viewsets.ModelViewSet):
    """Budget management viewset"""
    queryset = Budget.objects.all().select_related('created_by').order_by('-created_at')
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status']
    search_fields = ['name', 'description']
    ordering_fields = ['total_budget', 'start_date', 'created_at']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return BudgetListSerializer
        return BudgetSerializer

    def perform_create(self, serializer):
        """Set created_by to current user"""
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """Activate a budget"""
        budget = self.get_object()
        
        if budget.status != 'draft':
            return Response(
                {'error': 'يمكن تفعيل المسودات فقط'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        budget.status = 'active'
        budget.save()
        
        serializer = self.get_serializer(budget)
        return Response(serializer.data)


class FinancialKPIViewSet(viewsets.ModelViewSet):
    """Financial KPI management viewset"""
    queryset = FinancialKPI.objects.all().order_by('-period_start')
    serializer_class = FinancialKPISerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['type']
    ordering_fields = ['period_start', 'current_value', 'achievement_percentage']
    ordering = ['-period_start']

    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """Get KPI dashboard data"""
        current_date = timezone.now().date()
        current_month_start = current_date.replace(day=1)

        # Get current month KPIs
        current_kpis = self.get_queryset().filter(
            period_start__lte=current_date,
            period_end__gte=current_date
        )

        kpi_data = []
        for kpi in current_kpis:
            kpi_data.append({
                'name': kpi.name,
                'type': kpi.type,
                'current_value': float(kpi.current_value),
                'target_value': float(kpi.target_value),
                'achievement_percentage': float(kpi.achievement_percentage),
                'status': kpi.status,
                'growth_rate': float(kpi.growth_rate)
            })

        return Response({
            'current_period': current_month_start.strftime('%Y-%m'),
            'kpis': kpi_data
        })

    @action(detail=False, methods=['post'])
    def auto_calculate(self, request):
        """Auto-calculate KPI values from financial data"""
        period_start = request.data.get('period_start')
        period_end = request.data.get('period_end')

        if not period_start or not period_end:
            return Response(
                {'error': 'يجب تحديد تاريخ البداية والنهاية'},
                status=status.HTTP_400_BAD_REQUEST
            )

        period_start = datetime.strptime(period_start, '%Y-%m-%d').date()
        period_end = datetime.strptime(period_end, '%Y-%m-%d').date()

        # Calculate revenue growth
        current_revenue = RevenueStream.objects.filter(
            status='received',
            payment_date__gte=period_start,
            payment_date__lte=period_end
        ).aggregate(total=Sum('net_amount'))['total'] or 0

        # Calculate previous period revenue for growth rate
        period_length = (period_end - period_start).days
        prev_start = period_start - timedelta(days=period_length)
        prev_end = period_start - timedelta(days=1)

        prev_revenue = RevenueStream.objects.filter(
            status='received',
            payment_date__gte=prev_start,
            payment_date__lte=prev_end
        ).aggregate(total=Sum('net_amount'))['total'] or 0

        # Calculate expenses
        current_expenses = Expense.objects.filter(
            status='paid',
            payment_date__gte=period_start,
            payment_date__lte=period_end
        ).aggregate(total=Sum('net_amount'))['total'] or 0

        # Calculate profit margin
        profit = current_revenue - current_expenses
        profit_margin = (profit / current_revenue * 100) if current_revenue > 0 else 0

        # Calculate revenue growth
        revenue_growth = ((current_revenue - prev_revenue) / prev_revenue * 100) if prev_revenue > 0 else 0

        calculated_kpis = {
            'revenue_growth': float(revenue_growth),
            'profit_margin': float(profit_margin),
            'total_revenue': float(current_revenue),
            'total_expenses': float(current_expenses),
            'net_profit': float(profit)
        }

        return Response(calculated_kpis)


class FinancialDashboardViewSet(viewsets.ViewSet):
    """Financial dashboard data viewset"""
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['get'])
    def overview(self, request):
        """Get financial overview for dashboard"""
        current_date = timezone.now().date()
        current_month_start = current_date.replace(day=1)
        current_year_start = current_date.replace(month=1, day=1)

        # Revenue metrics
        total_revenue = RevenueStream.objects.filter(
            status='received'
        ).aggregate(total=Sum('net_amount'))['total'] or 0

        monthly_revenue = RevenueStream.objects.filter(
            status='received',
            payment_date__gte=current_month_start
        ).aggregate(total=Sum('net_amount'))['total'] or 0

        yearly_revenue = RevenueStream.objects.filter(
            status='received',
            payment_date__gte=current_year_start
        ).aggregate(total=Sum('net_amount'))['total'] or 0

        # Expense metrics
        total_expenses = Expense.objects.filter(
            status='paid'
        ).aggregate(total=Sum('net_amount'))['total'] or 0

        monthly_expenses = Expense.objects.filter(
            status='paid',
            payment_date__gte=current_month_start
        ).aggregate(total=Sum('net_amount'))['total'] or 0

        yearly_expenses = Expense.objects.filter(
            status='paid',
            payment_date__gte=current_year_start
        ).aggregate(total=Sum('net_amount'))['total'] or 0

        # Pending items
        pending_invoices = RevenueStream.objects.filter(status='pending').count()
        overdue_invoices = RevenueStream.objects.filter(
            status='pending',
            due_date__lt=current_date
        ).count()
        pending_expenses = Expense.objects.filter(status='pending').count()

        # Calculate profit margins
        monthly_profit = monthly_revenue - monthly_expenses
        yearly_profit = yearly_revenue - yearly_expenses
        total_profit = total_revenue - total_expenses

        monthly_margin = (monthly_profit / monthly_revenue * 100) if monthly_revenue > 0 else 0
        yearly_margin = (yearly_profit / yearly_revenue * 100) if yearly_revenue > 0 else 0
        total_margin = (total_profit / total_revenue * 100) if total_revenue > 0 else 0

        overview_data = {
            'revenue': {
                'total': float(total_revenue),
                'monthly': float(monthly_revenue),
                'yearly': float(yearly_revenue)
            },
            'expenses': {
                'total': float(total_expenses),
                'monthly': float(monthly_expenses),
                'yearly': float(yearly_expenses)
            },
            'profit': {
                'total': float(total_profit),
                'monthly': float(monthly_profit),
                'yearly': float(yearly_profit)
            },
            'margins': {
                'total': float(total_margin),
                'monthly': float(monthly_margin),
                'yearly': float(yearly_margin)
            },
            'pending': {
                'invoices': pending_invoices,
                'overdue_invoices': overdue_invoices,
                'expenses': pending_expenses
            }
        }

        return Response(overview_data)

    @action(detail=False, methods=['get'])
    def department_analysis(self, request):
        """Get financial analysis by department"""
        departments = ['sales', 'development', 'design', 'media_buying']
        department_data = []

        for dept in departments:
            # Revenue from projects handled by this department
            dept_revenue = RevenueStream.objects.filter(
                status='received',
                project__team_members__department=dept
            ).aggregate(total=Sum('net_amount'))['total'] or 0

            # Expenses for this department (team salaries + project costs)
            dept_expenses = Expense.objects.filter(
                status='paid',
                team_member__department=dept
            ).aggregate(total=Sum('net_amount'))['total'] or 0

            # Project count
            from projects.models import Project
            project_count = Project.objects.filter(
                team_members__department=dept
            ).distinct().count()

            # Average project value
            avg_project_value = dept_revenue / project_count if project_count > 0 else 0

            # Profit margin
            dept_profit = dept_revenue - dept_expenses
            profit_margin = (dept_profit / dept_revenue * 100) if dept_revenue > 0 else 0

            department_data.append({
                'department': dept,
                'total_revenue': float(dept_revenue),
                'total_expenses': float(dept_expenses),
                'profit_margin': float(profit_margin),
                'team_cost': float(dept_expenses),
                'project_count': project_count,
                'avg_project_value': float(avg_project_value)
            })

        return Response(department_data)

    @action(detail=False, methods=['get'])
    def trends(self, request):
        """Get financial trends data"""
        months = int(request.query_params.get('months', 12))
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30 * months)

        trends_data = []

        for i in range(months):
            month_start = start_date + timedelta(days=30 * i)
            month_end = month_start + timedelta(days=30)

            month_revenue = RevenueStream.objects.filter(
                status='received',
                payment_date__gte=month_start,
                payment_date__lt=month_end
            ).aggregate(total=Sum('net_amount'))['total'] or 0

            month_expenses = Expense.objects.filter(
                status='paid',
                payment_date__gte=month_start,
                payment_date__lt=month_end
            ).aggregate(total=Sum('net_amount'))['total'] or 0

            month_profit = month_revenue - month_expenses

            trends_data.append({
                'month': month_start.strftime('%Y-%m'),
                'revenue': float(month_revenue),
                'expenses': float(month_expenses),
                'profit': float(month_profit)
            })

        return Response(trends_data)

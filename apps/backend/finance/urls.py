from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    RevenueStreamViewSet, ExpenseViewSet, ExpenseCategoryViewSet,
    CashFlowProjectionViewSet, BudgetViewSet, FinancialKPIViewSet,
    FinancialDashboardViewSet
)

router = DefaultRouter()
router.register(r'revenue', RevenueStreamViewSet, basename='revenue')
router.register(r'expenses', ExpenseViewSet, basename='expenses')
router.register(r'expense-categories', ExpenseCategoryViewSet, basename='expense-categories')
router.register(r'cash-flow', CashFlowProjectionViewSet, basename='cash-flow')
router.register(r'budgets', BudgetViewSet, basename='budgets')
router.register(r'kpis', FinancialKPIViewSet, basename='kpis')
router.register(r'dashboard', FinancialDashboardViewSet, basename='dashboard')

urlpatterns = [
    path('', include(router.urls)),
]

from rest_framework import serializers
from .models import (
    RevenueStream, Expense, ExpenseCategory, CashFlowProjection,
    Budget, BudgetAllocation, FinancialKPI
)
from clients.serializers import ClientListSerializer
from projects.serializers import ProjectListSerializer
from authentication.serializers import UserListSerializer


class ExpenseCategorySerializer(serializers.ModelSerializer):
    """Expense category serializer"""
    
    class Meta:
        model = ExpenseCategory
        fields = [
            'id', 'name', 'description', 'parent', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class RevenueStreamSerializer(serializers.ModelSerializer):
    """Revenue stream serializer with all fields"""
    client = ClientListSerializer(read_only=True)
    project = ProjectListSerializer(read_only=True)
    sales_rep = UserListSerializer(read_only=True)
    client_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    project_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    sales_rep_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    type_display = serializers.CharField(source='get_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    is_overdue = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = RevenueStream
        fields = [
            'id', 'title', 'description', 'type', 'type_display', 'status', 'status_display',
            'amount', 'tax_amount', 'net_amount', 'client', 'client_id', 'project', 'project_id',
            'sales_rep', 'sales_rep_id', 'invoice_date', 'due_date', 'payment_date',
            'is_overdue', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'net_amount', 'created_at', 'updated_at']


class RevenueStreamListSerializer(serializers.ModelSerializer):
    """Revenue stream list serializer with minimal fields"""
    client_name = serializers.CharField(source='client.name', read_only=True)
    project_name = serializers.CharField(source='project.name', read_only=True)
    type_display = serializers.CharField(source='get_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    is_overdue = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = RevenueStream
        fields = [
            'id', 'title', 'type', 'type_display', 'status', 'status_display',
            'amount', 'net_amount', 'client_name', 'project_name',
            'invoice_date', 'due_date', 'payment_date', 'is_overdue'
        ]


class ExpenseSerializer(serializers.ModelSerializer):
    """Expense serializer with all fields"""
    category = ExpenseCategorySerializer(read_only=True)
    team_member_name = serializers.CharField(source='team_member.full_name', read_only=True)
    project_name = serializers.CharField(source='project.name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.get_full_name', read_only=True)
    category_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    team_member_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    project_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    approved_by_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    type_display = serializers.CharField(source='get_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = Expense
        fields = [
            'id', 'title', 'description', 'type', 'type_display', 'status', 'status_display',
            'category', 'category_id', 'amount', 'tax_amount', 'net_amount',
            'team_member_name', 'team_member_id', 'project_name', 'project_id',
            'approved_by_name', 'approved_by_id', 'expense_date', 'due_date', 'payment_date',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'net_amount', 'created_at', 'updated_at']


class ExpenseListSerializer(serializers.ModelSerializer):
    """Expense list serializer with minimal fields"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    team_member_name = serializers.CharField(source='team_member.full_name', read_only=True)
    type_display = serializers.CharField(source='get_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = Expense
        fields = [
            'id', 'title', 'type', 'type_display', 'status', 'status_display',
            'category_name', 'amount', 'net_amount', 'team_member_name',
            'expense_date', 'due_date', 'payment_date'
        ]


class CashFlowProjectionSerializer(serializers.ModelSerializer):
    """Cash flow projection serializer"""
    period_type_display = serializers.CharField(source='get_period_type_display', read_only=True)
    revenue_variance = serializers.DecimalField(max_digits=5, decimal_places=2, read_only=True)
    expense_variance = serializers.DecimalField(max_digits=5, decimal_places=2, read_only=True)
    
    class Meta:
        model = CashFlowProjection
        fields = [
            'id', 'title', 'period_type', 'period_type_display', 'period_start', 'period_end',
            'projected_revenue', 'projected_expenses', 'projected_profit',
            'actual_revenue', 'actual_expenses', 'actual_profit',
            'revenue_variance', 'expense_variance', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'projected_profit', 'actual_profit', 'created_at', 'updated_at']


class BudgetAllocationSerializer(serializers.ModelSerializer):
    """Budget allocation serializer"""
    category = ExpenseCategorySerializer(read_only=True)
    category_id = serializers.IntegerField(write_only=True)
    remaining_amount = serializers.DecimalField(max_digits=14, decimal_places=2, read_only=True)
    utilization_percentage = serializers.DecimalField(max_digits=5, decimal_places=2, read_only=True)
    
    class Meta:
        model = BudgetAllocation
        fields = [
            'id', 'category', 'category_id', 'allocated_amount', 'spent_amount',
            'remaining_amount', 'utilization_percentage', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class BudgetSerializer(serializers.ModelSerializer):
    """Budget serializer with all fields"""
    allocations = BudgetAllocationSerializer(many=True, read_only=True)
    created_by = UserListSerializer(read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    utilization_percentage = serializers.DecimalField(max_digits=5, decimal_places=2, read_only=True)
    is_over_budget = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = Budget
        fields = [
            'id', 'name', 'description', 'status', 'status_display', 'start_date', 'end_date',
            'total_budget', 'allocated_amount', 'spent_amount', 'remaining_amount',
            'utilization_percentage', 'is_over_budget', 'created_by', 'allocations',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'remaining_amount', 'created_by', 'created_at', 'updated_at']


class BudgetListSerializer(serializers.ModelSerializer):
    """Budget list serializer with minimal fields"""
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    utilization_percentage = serializers.DecimalField(max_digits=5, decimal_places=2, read_only=True)
    is_over_budget = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = Budget
        fields = [
            'id', 'name', 'status', 'status_display', 'start_date', 'end_date',
            'total_budget', 'spent_amount', 'remaining_amount',
            'utilization_percentage', 'is_over_budget'
        ]


class FinancialKPISerializer(serializers.ModelSerializer):
    """Financial KPI serializer"""
    type_display = serializers.CharField(source='get_type_display', read_only=True)
    achievement_percentage = serializers.DecimalField(max_digits=5, decimal_places=2, read_only=True)
    growth_rate = serializers.DecimalField(max_digits=5, decimal_places=2, read_only=True)
    status = serializers.CharField(read_only=True)
    
    class Meta:
        model = FinancialKPI
        fields = [
            'id', 'name', 'type', 'type_display', 'description', 'target_value',
            'current_value', 'previous_value', 'achievement_percentage', 'growth_rate',
            'status', 'period_start', 'period_end', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


# Financial Dashboard Serializers
class FinancialSummarySerializer(serializers.Serializer):
    """Financial summary serializer for dashboard"""
    total_revenue = serializers.DecimalField(max_digits=14, decimal_places=2)
    total_expenses = serializers.DecimalField(max_digits=14, decimal_places=2)
    net_profit = serializers.DecimalField(max_digits=14, decimal_places=2)
    profit_margin = serializers.DecimalField(max_digits=5, decimal_places=2)
    pending_invoices = serializers.IntegerField()
    overdue_invoices = serializers.IntegerField()
    pending_expenses = serializers.IntegerField()
    cash_flow_trend = serializers.CharField()


class DepartmentFinancialSerializer(serializers.Serializer):
    """Department-specific financial data serializer"""
    department = serializers.CharField()
    total_revenue = serializers.DecimalField(max_digits=14, decimal_places=2)
    total_expenses = serializers.DecimalField(max_digits=14, decimal_places=2)
    profit_margin = serializers.DecimalField(max_digits=5, decimal_places=2)
    team_cost = serializers.DecimalField(max_digits=14, decimal_places=2)
    project_count = serializers.IntegerField()
    avg_project_value = serializers.DecimalField(max_digits=14, decimal_places=2)
